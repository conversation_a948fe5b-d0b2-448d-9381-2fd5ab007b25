<template>
  <view class="avatar-test">
    <view class="test-header">
      <text class="title">头像绘制测试</text>
    </view>

    <view class="test-controls">
      <button class="test-btn" @click="testBasicDraw">测试基本绘制</button>
      <button class="test-btn" @click="testImageDraw">测试图片绘制</button>
      <button class="test-btn" @click="testAvatarDraw">测试头像绘制</button>
      <button class="test-btn" @click="testCanvasFeatures">测试Canvas功能</button>
    </view>

    <view class="canvas-container">
      <canvas class="test-canvas" canvas-id="testCanvas"
        :style="{ width: '300px', height: '300px', border: '1px solid #ccc' }"></canvas>
    </view>

    <view class="test-logs">
      <text class="log-title">测试日志:</text>
      <view class="log-item" v-for="(log, index) in logs" :key="index">
        <text class="log-text">{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { imageHelper } from '@/utils/imageHelper.js'
import { drawImageFixed, canvasImageFix } from '@/utils/canvasImageFix.js'

export default {
  name: 'SimpleAvatarTest',

  data() {
    return {
      logs: []
    }
  },

  methods: {
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.logs.unshift(`[${timestamp}] ${message}`)
      console.log(message)

      // 限制日志数量
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20)
      }
    },

    // 测试基本绘制功能
    testBasicDraw() {
      this.addLog('开始测试基本绘制功能...')

      const ctx = uni.createCanvasContext('testCanvas', this)

      // 清空画布
      ctx.clearRect(0, 0, 300, 300)

      // 绘制背景
      ctx.setFillStyle('#f0f0f0')
      ctx.fillRect(0, 0, 300, 300)

      // 绘制圆形
      ctx.setFillStyle('#007AFF')
      ctx.beginPath()
      ctx.arc(150, 150, 50, 0, 2 * Math.PI)
      ctx.fill()

      // 绘制文字
      ctx.setFillStyle('#333333')
      ctx.setFontSize(16)
      ctx.setTextAlign('center')
      ctx.fillText('基本绘制测试', 150, 250)

      ctx.draw(false, () => {
        this.addLog('✅ 基本绘制功能正常')
      })
    },

    // 测试图片绘制功能
    async testImageDraw() {
      this.addLog('开始测试图片绘制功能...')

      try {
        const ctx = uni.createCanvasContext('testCanvas', this)

        // 清空画布
        ctx.clearRect(0, 0, 300, 300)
        ctx.setFillStyle('#ffffff')
        ctx.fillRect(0, 0, 300, 300)

        // 测试用的图片URL
        const testImageUrl = 'https://picsum.photos/100/100?random=test'
        this.addLog(`下载测试图片: ${testImageUrl}`)

        // 下载图片
        const imagePath = await imageHelper.downloadImage(testImageUrl)
        this.addLog(`图片下载成功: ${imagePath}`)

        // 使用修复工具绘制图片
        const success = await drawImageFixed(ctx, imagePath, 100, 100, 100, 100, {
          isCircular: false,
          borderColor: '#007AFF',
          borderWidth: 2
        })

        if (!success) {
          this.addLog('图片绘制失败，使用了占位符')
        }

        // 绘制说明文字
        ctx.setFillStyle('#333333')
        ctx.setFontSize(14)
        ctx.setTextAlign('center')
        ctx.fillText('图片绘制测试', 150, 250)

        ctx.draw(false, () => {
          this.addLog('✅ 图片绘制功能正常')
        })

      } catch (error) {
        this.addLog(`❌ 图片绘制失败: ${error.message}`)
      }
    },

    // 测试头像绘制功能
    async testAvatarDraw() {
      this.addLog('开始测试头像绘制功能...')

      try {
        const ctx = uni.createCanvasContext('testCanvas', this)

        // 清空画布
        ctx.clearRect(0, 0, 300, 300)
        ctx.setFillStyle('#ffffff')
        ctx.fillRect(0, 0, 300, 300)

        // 测试用的头像URL
        const avatarUrl = 'https://picsum.photos/80/80?random=avatar'
        this.addLog(`下载头像: ${avatarUrl}`)

        // 下载头像
        const imagePath = await imageHelper.downloadImage(avatarUrl)
        this.addLog(`头像下载成功: ${imagePath}`)

        // 使用修复工具绘制圆形头像
        const success = await drawImageFixed(ctx, imagePath, 110, 110, 80, 80, {
          isCircular: true,
          borderColor: '#007AFF',
          borderWidth: 2,
          backgroundColor: '#e0e0e0'
        })

        if (success) {
          this.addLog('✅ 圆形头像绘制成功')
        } else {
          this.addLog('⚠️ 头像绘制部分成功（使用占位符）')
        }

        // 绘制说明文字
        ctx.setFillStyle('#333333')
        ctx.setFontSize(14)
        ctx.setTextAlign('center')
        ctx.fillText('头像绘制测试', 150, 250)

        ctx.draw(false, () => {
          this.addLog('✅ 头像绘制功能正常')
        })

      } catch (error) {
        this.addLog(`❌ 头像绘制失败: ${error.message}`)
      }
    },

    // 绘制圆形遮罩
    drawCircularMask(ctx, x, y, size) {
      return new Promise((resolve) => {
        try {
          const centerX = x + size / 2
          const centerY = y + size / 2
          const radius = size / 2

          // 保存当前状态
          ctx.save()

          // 设置全局合成操作
          ctx.globalCompositeOperation = 'destination-in'

          // 绘制圆形
          ctx.beginPath()
          ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
          ctx.setFillStyle('#000000')
          ctx.fill()

          // 恢复状态
          ctx.restore()

          // 绘制圆形边框
          ctx.beginPath()
          ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
          ctx.setStrokeStyle('#007AFF')
          ctx.setLineWidth(2)
          ctx.stroke()

          this.addLog('圆形遮罩处理完成')
          resolve()

        } catch (error) {
          this.addLog(`圆形遮罩处理失败: ${error.message}`)
          resolve() // 不阻塞流程
        }
      })
    },

    // 测试Canvas功能
    async testCanvasFeatures() {
      this.addLog('开始测试Canvas功能...')

      try {
        const ctx = uni.createCanvasContext('testCanvas', this)

        // 清空画布
        ctx.clearRect(0, 0, 300, 300)
        ctx.setFillStyle('#ffffff')
        ctx.fillRect(0, 0, 300, 300)

        // 使用修复工具测试Canvas功能
        const testResults = await canvasImageFix.testCanvasDrawing(ctx)

        this.addLog(`平台: ${testResults.platform}`)
        this.addLog(`基本绘制: ${testResults.basicDraw ? '✅' : '❌'}`)
        this.addLog(`图片绘制: ${testResults.imageDraw ? '✅' : '❌'}`)
        this.addLog(`圆形裁剪: ${testResults.circularClip ? '✅' : '❌'}`)

        // 绘制说明文字
        ctx.setFillStyle('#333333')
        ctx.setFontSize(14)
        ctx.setTextAlign('center')
        ctx.fillText('Canvas功能测试', 150, 280)

        ctx.draw(false, () => {
          this.addLog('Canvas功能测试完成')
        })

      } catch (error) {
        this.addLog(`❌ Canvas功能测试失败: ${error.message}`)
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = []
    }
  }
}
</script>

<style scoped>
.avatar-test {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.test-controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.test-btn {
  flex: 1;
  height: 70rpx;
  background-color: #007AFF;
  color: #fff;
  border: none;
  border-radius: 35rpx;
  font-size: 26rpx;
  min-width: 200rpx;
}

.canvas-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 12rpx;
}

.test-canvas {
  border: 1px solid #ccc;
}

.test-logs {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.log-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.log-item {
  margin-bottom: 10rpx;
  padding: 8rpx 12rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
}

.log-text {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}
</style>
