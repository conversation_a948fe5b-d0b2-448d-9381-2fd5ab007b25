// Canvas图片绘制修复工具
// 专门解决uniapp中Canvas绘制图片的兼容性问题

export class CanvasImageFix {
  constructor() {
    this.platform = this.detectPlatform()
    console.log('检测到平台:', this.platform)
  }

  // 检测平台
  detectPlatform() {
    // #ifdef MP-WEIXIN
    return 'mp-weixin'
    // #endif
    
    // #ifdef H5
    return 'h5'
    // #endif
    
    // #ifdef APP-PLUS
    return 'app-plus'
    // #endif
    
    return 'unknown'
  }

  // 修复Canvas绘制图片问题
  async drawImageFixed(ctx, imagePath, x, y, width, height, options = {}) {
    const {
      isCircular = false,
      borderColor = '#e0e0e0',
      borderWidth = 2,
      backgroundColor = '#f0f0f0'
    } = options

    console.log('开始修复版图片绘制:', {
      imagePath,
      x, y, width, height,
      platform: this.platform,
      isCircular
    })

    try {
      // 先绘制背景
      ctx.setFillStyle(backgroundColor)
      if (isCircular) {
        ctx.beginPath()
        ctx.arc(x + width/2, y + height/2, width/2, 0, 2 * Math.PI)
        ctx.fill()
      } else {
        ctx.fillRect(x, y, width, height)
      }

      // 根据平台使用不同的绘制策略
      switch (this.platform) {
        case 'mp-weixin':
          await this.drawImageWechat(ctx, imagePath, x, y, width, height, isCircular)
          break
        case 'h5':
          await this.drawImageH5(ctx, imagePath, x, y, width, height, isCircular)
          break
        case 'app-plus':
          await this.drawImageApp(ctx, imagePath, x, y, width, height, isCircular)
          break
        default:
          await this.drawImageDefault(ctx, imagePath, x, y, width, height, isCircular)
      }

      // 绘制边框
      this.drawBorder(ctx, x, y, width, height, isCircular, borderColor, borderWidth)

      console.log('✅ 修复版图片绘制成功')
      return true

    } catch (error) {
      console.error('修复版图片绘制失败:', error)
      
      // 绘制占位符
      this.drawPlaceholder(ctx, x, y, width, height, isCircular, '图片')
      return false
    }
  }

  // 微信小程序版本
  async drawImageWechat(ctx, imagePath, x, y, width, height, isCircular) {
    console.log('使用微信小程序绘制方法')
    
    if (isCircular) {
      // 保存状态
      ctx.save()
      
      // 创建圆形裁剪区域
      ctx.beginPath()
      ctx.arc(x + width/2, y + height/2, width/2, 0, 2 * Math.PI)
      ctx.clip()
      
      // 绘制图片
      ctx.drawImage(imagePath, x, y, width, height)
      
      // 恢复状态
      ctx.restore()
    } else {
      ctx.drawImage(imagePath, x, y, width, height)
    }
  }

  // H5版本
  async drawImageH5(ctx, imagePath, x, y, width, height, isCircular) {
    console.log('使用H5绘制方法')
    
    // H5环境下的特殊处理
    if (isCircular) {
      ctx.save()
      ctx.beginPath()
      ctx.arc(x + width/2, y + height/2, width/2, 0, 2 * Math.PI)
      ctx.clip()
    }
    
    ctx.drawImage(imagePath, x, y, width, height)
    
    if (isCircular) {
      ctx.restore()
    }
  }

  // App版本
  async drawImageApp(ctx, imagePath, x, y, width, height, isCircular) {
    console.log('使用App绘制方法')
    
    // App环境的特殊处理
    if (isCircular) {
      // 使用遮罩方式
      ctx.drawImage(imagePath, x, y, width, height)
      
      // 绘制遮罩
      ctx.save()
      ctx.globalCompositeOperation = 'destination-in'
      ctx.beginPath()
      ctx.arc(x + width/2, y + height/2, width/2, 0, 2 * Math.PI)
      ctx.setFillStyle('#000000')
      ctx.fill()
      ctx.restore()
    } else {
      ctx.drawImage(imagePath, x, y, width, height)
    }
  }

  // 默认版本
  async drawImageDefault(ctx, imagePath, x, y, width, height, isCircular) {
    console.log('使用默认绘制方法')
    
    // 最简单的方式
    ctx.drawImage(imagePath, x, y, width, height)
    
    // 如果需要圆形，用边框模拟
    if (isCircular) {
      // 不做裁剪，只是用圆形边框来视觉上模拟圆形效果
      console.log('使用边框模拟圆形效果')
    }
  }

  // 绘制边框
  drawBorder(ctx, x, y, width, height, isCircular, borderColor, borderWidth) {
    ctx.setStrokeStyle(borderColor)
    ctx.setLineWidth(borderWidth)
    
    if (isCircular) {
      ctx.beginPath()
      ctx.arc(x + width/2, y + height/2, width/2 - borderWidth/2, 0, 2 * Math.PI)
      ctx.stroke()
    } else {
      ctx.strokeRect(x, y, width, height)
    }
  }

  // 绘制占位符
  drawPlaceholder(ctx, x, y, width, height, isCircular, text = '图片') {
    console.log('绘制占位符:', text)
    
    // 背景
    ctx.setFillStyle('#f5f5f5')
    if (isCircular) {
      ctx.beginPath()
      ctx.arc(x + width/2, y + height/2, width/2, 0, 2 * Math.PI)
      ctx.fill()
    } else {
      ctx.fillRect(x, y, width, height)
    }
    
    // 文字
    ctx.setFillStyle('#cccccc')
    ctx.setFontSize(Math.min(width, height) * 0.2)
    ctx.setTextAlign('center')
    ctx.fillText(text, x + width/2, y + height/2 + 5)
    
    // 边框
    this.drawBorder(ctx, x, y, width, height, isCircular, '#e0e0e0', 1)
  }

  // 测试Canvas绘制功能
  async testCanvasDrawing(ctx) {
    console.log('开始测试Canvas绘制功能...')
    
    const testResults = {
      platform: this.platform,
      basicDraw: false,
      imageDraw: false,
      circularClip: false
    }

    try {
      // 测试基本绘制
      ctx.setFillStyle('#007AFF')
      ctx.fillRect(10, 10, 50, 50)
      testResults.basicDraw = true
      console.log('✅ 基本绘制测试通过')
    } catch (error) {
      console.error('❌ 基本绘制测试失败:', error)
    }

    try {
      // 测试圆形裁剪
      ctx.save()
      ctx.beginPath()
      ctx.arc(100, 35, 25, 0, 2 * Math.PI)
      ctx.clip()
      ctx.setFillStyle('#FF6B6B')
      ctx.fillRect(75, 10, 50, 50)
      ctx.restore()
      testResults.circularClip = true
      console.log('✅ 圆形裁剪测试通过')
    } catch (error) {
      console.error('❌ 圆形裁剪测试失败:', error)
    }

    return testResults
  }
}

// 创建单例
export const canvasImageFix = new CanvasImageFix()

// 便捷方法
export const drawImageFixed = (ctx, imagePath, x, y, width, height, options) => {
  return canvasImageFix.drawImageFixed(ctx, imagePath, x, y, width, height, options)
}

// 使用示例:
// import { drawImageFixed } from '@/utils/canvasImageFix.js'
// 
// // 绘制圆形头像
// await drawImageFixed(ctx, imagePath, x, y, 80, 80, {
//   isCircular: true,
//   borderColor: '#e0e0e0',
//   borderWidth: 2
// })
// 
// // 绘制矩形图片
// await drawImageFixed(ctx, imagePath, x, y, 200, 150, {
//   isCircular: false,
//   backgroundColor: '#f5f5f5'
// })
